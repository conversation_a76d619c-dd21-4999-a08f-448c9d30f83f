"""
Python wrapper for the Rust data processing module.

This module provides a clean Python interface to the high-performance Rust functions
for financial data processing, with automatic data type conversions and pandas integration.
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional, Union
import warnings

try:
    import rust_data_processor
    RUST_AVAILABLE = True
except ImportError as e:
    RUST_AVAILABLE = False
    warnings.warn(f"Rust data processor not available: {e}. Falling back to pure Python implementation.")

try:
    import polars as pl
    POLARS_AVAILABLE = True
except ImportError:
    POLARS_AVAILABLE = False
    if RUST_AVAILABLE:
        warnings.warn("Polars not available. Some optimizations may not work.")


class RustDataProcessor:
    """
    High-performance data processor using Rust backend.
    
    This class provides optimized implementations of common financial data processing
    operations like resampling, aggregation, and filtering.
    """
    
    def __init__(self):
        if not RUST_AVAILABLE:
            raise ImportError("Rust data processor module not available. Please build the extension first.")
        if not POLARS_AVAILABLE:
            warnings.warn("Polars not available. Some optimizations may be limited.")
    
    def process_chunk_data(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        Process a chunk of financial data, separating futures and options,
        and trade vs order data.

        Args:
            df: DataFrame with columns including 'strike_price', 'close', 'ord_price'

        Returns:
            Dictionary with keys: 'fut_trd', 'fut_ord', 'opt_trd', 'opt_ord'
        """
        # Convert pandas DataFrame to Polars for Rust processing
        try:
            # Convert to Polars DataFrame
            polars_df = pl.from_pandas(df.reset_index())

            # Call the new Rust function that uses Polars internally
            result_dict = rust_data_processor.process_chunk_data_fast(polars_df)

            # Convert results back to pandas
            result = {}
            for key, polars_df in result_dict.items():
                if hasattr(polars_df, 'to_pandas'):
                    pandas_df = polars_df.to_pandas()
                    if 'timestamp' in pandas_df.columns:
                        pandas_df.set_index('timestamp', inplace=True)
                    result[key] = pandas_df
                else:
                    result[key] = pd.DataFrame()

            return result

        except Exception as e:
            # Fallback to Python implementation if Rust fails
            warnings.warn(f"Rust processing failed: {e}. Using Python fallback.")
            return self._python_process_chunk_data(df)
    
    def resample_futures_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """
        Resample futures trade data to OHLC format.

        Args:
            df: DataFrame with timestamp index and columns: symbol, expiry, close, volume
            interval_minutes: Resampling interval in minutes

        Returns:
            Resampled DataFrame with OHLC data
        """
        if df.empty:
            return df

        try:
            # Convert to Polars DataFrame
            polars_df = pl.from_pandas(df.reset_index())

            # Call the new Rust function
            result_polars = rust_data_processor.resample_futures_trade_fast(polars_df, interval_minutes)

            # Convert back to pandas
            result_df = result_polars.to_pandas()
            if 'timestamp' in result_df.columns:
                result_df.set_index('timestamp', inplace=True)

            return result_df.sort_index()

        except Exception as e:
            # Fallback to Python implementation
            warnings.warn(f"Rust resampling failed: {e}. Using Python fallback.")
            return self._python_resample_futures_trade(df, interval_minutes)
    
    def resample_futures_order(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """
        Resample futures order data to OHLC format.

        Args:
            df: DataFrame with timestamp index and columns: symbol, expiry, ord_price
            interval_minutes: Resampling interval in minutes

        Returns:
            Resampled DataFrame with OHLC data
        """
        if df.empty:
            return df

        try:
            # Convert to Polars DataFrame
            polars_df = pl.from_pandas(df.reset_index())

            # Call the new Rust function
            result_polars = rust_data_processor.resample_futures_order_fast(polars_df, interval_minutes)

            # Convert back to pandas
            result_df = result_polars.to_pandas()
            if 'timestamp' in result_df.columns:
                result_df.set_index('timestamp', inplace=True)

            return result_df.sort_index()

        except Exception as e:
            # Fallback to Python implementation
            warnings.warn(f"Rust resampling failed: {e}. Using Python fallback.")
            return self._python_resample_futures_order(df, interval_minutes)
    
    def resample_options_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """
        Resample options trade data to OHLC format.

        Args:
            df: DataFrame with timestamp index and columns: symbol, expiry, strike_price, option_type, close
            interval_minutes: Resampling interval in minutes

        Returns:
            Resampled DataFrame with OHLC data
        """
        if df.empty:
            return df

        try:
            # Convert to Polars DataFrame
            polars_df = pl.from_pandas(df.reset_index())

            # Call the new Rust function
            result_polars = rust_data_processor.resample_options_trade_fast(polars_df, interval_minutes)

            # Convert back to pandas
            result_df = result_polars.to_pandas()
            if 'timestamp' in result_df.columns:
                result_df.set_index('timestamp', inplace=True)

            return result_df.sort_index()

        except Exception as e:
            # Fallback to Python implementation
            warnings.warn(f"Rust resampling failed: {e}. Using Python fallback.")
            return self._python_resample_options_trade(df, interval_minutes)

    # Python fallback methods
    def _python_process_chunk_data(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Pure Python implementation of chunk data processing."""
        df_fut = df[df.strike_price.isna()].drop(columns=["strike_price", "option_type"])
        df_opt = df[~df.strike_price.isna()]

        return {
            'fut_trd': df_fut[df_fut.close != 0].drop(columns=["ord_price"]),
            'fut_ord': df_fut[df_fut.ord_price != 0].drop(columns=["close", "volume"]),
            'opt_trd': df_opt[df_opt.close != 0].drop(columns=["ord_price"]),
            'opt_ord': df_opt[df_opt.ord_price != 0].drop(columns=["close", "volume"])
        }

    def _python_resample_futures_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """Pure Python implementation of futures trade resampling."""
        if df.empty:
            return df

        result = (
            df.groupby(["symbol", "expiry"])
            .resample(f"{interval_minutes}min", label="right", closed="right")
            .agg({
                "close": ["first", "max", "min", "last"],
                "volume": "sum"
            })
            .dropna()
        )

        # Flatten column names
        result.columns = ["_".join(col).strip() for col in result.columns.values]
        result = result.rename(columns={
            "close_first": "open",
            "close_max": "high",
            "close_min": "low",
            "close_last": "close",
            "volume_sum": "volume",
        })

        return result.reset_index().set_index("timestamp").sort_index()

    def _python_resample_futures_order(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """Pure Python implementation of futures order resampling."""
        if df.empty:
            return df

        result = (
            df.groupby(["symbol", "expiry"])
            .resample(f"{interval_minutes}min", label="right", closed="right")
            .agg({
                "ord_price": ["first", "max", "min", "last"]
            })
            .dropna()
        )

        # Flatten column names
        result.columns = ["_".join(col).strip() for col in result.columns.values]
        result = result.rename(columns={
            "ord_price_first": "open",
            "ord_price_max": "high",
            "ord_price_min": "low",
            "ord_price_last": "close",
        })

        return result.reset_index().set_index("timestamp").sort_index()

    def _python_resample_options_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """Pure Python implementation of options trade resampling."""
        if df.empty:
            return df

        result = (
            df.groupby(["symbol", "expiry", "strike_price", "option_type"])
            .resample(f"{interval_minutes}min", label="right", closed="right")
            .agg({
                "close": ["first", "max", "min", "last"]
            })
            .dropna()
        )

        # Flatten column names
        result.columns = ["_".join(col).strip() for col in result.columns.values]
        result = result.rename(columns={
            "close_first": "open",
            "close_max": "high",
            "close_min": "low",
            "close_last": "close",
        })

        return result.reset_index().set_index("timestamp").sort_index()


# Fallback pure Python implementations
class PythonDataProcessor:
    """
    Pure Python fallback implementation for when Rust is not available.
    """
    
    def process_chunk_data(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Pure Python implementation of chunk data processing."""
        df_fut = df[df.strike_price.isna()].drop(columns=["strike_price", "option_type"])
        df_opt = df[~df.strike_price.isna()]
        
        result = {
            'fut_trd': df_fut[df_fut.close != 0].drop(columns=["ord_price"]),
            'fut_ord': df_fut[df_fut.ord_price != 0].drop(columns=["close", "volume"]),
            'opt_trd': df_opt[df_opt.close != 0].drop(columns=["ord_price"]),
            'opt_ord': df_opt[df_opt.ord_price != 0].drop(columns=["close", "volume"])
        }
        
        return result
    
    def resample_futures_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """Pure Python implementation of futures trade resampling."""
        if df.empty:
            return df
        
        return (
            df.groupby(["symbol", "expiry"])
            .resample(f"{interval_minutes}min", label="right", closed="right")
            .agg({
                "close": ["first", "max", "min", "last"],
                "volume": "sum"
            })
            .dropna()
        )
    
    def resample_futures_order(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """Pure Python implementation of futures order resampling."""
        if df.empty:
            return df
        
        return (
            df.groupby(["symbol", "expiry"])
            .resample(f"{interval_minutes}min", label="right", closed="right")
            .agg({
                "ord_price": ["first", "max", "min", "last"]
            })
            .dropna()
        )
    
    def resample_options_trade(self, df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
        """Pure Python implementation of options trade resampling."""
        if df.empty:
            return df
        
        return (
            df.groupby(["symbol", "expiry", "strike_price", "option_type"])
            .resample(f"{interval_minutes}min", label="right", closed="right")
            .agg({
                "close": ["first", "max", "min", "last"]
            })
            .dropna()
        )


# Create the appropriate processor instance
if RUST_AVAILABLE:
    data_processor = RustDataProcessor()
else:
    data_processor = PythonDataProcessor()


# Convenience functions
def process_chunk_data(df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    """Process chunk data using the best available implementation."""
    return data_processor.process_chunk_data(df)


def resample_futures_trade(df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
    """Resample futures trade data using the best available implementation."""
    return data_processor.resample_futures_trade(df, interval_minutes)


def resample_futures_order(df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
    """Resample futures order data using the best available implementation."""
    return data_processor.resample_futures_order(df, interval_minutes)


def resample_options_trade(df: pd.DataFrame, interval_minutes: int = 1) -> pd.DataFrame:
    """Resample options trade data using the best available implementation."""
    return data_processor.resample_options_trade(df, interval_minutes)
