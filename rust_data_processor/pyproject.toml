[build-system]
requires = ["maturin>=1.0,<2.0"]
build-backend = "maturin"

[project]
name = "rust-data-processor"
version = "0.1.0"
description = "High-performance data processing for financial data using Rust and PyO3"
authors = [
    {name = "Data Auditing Team"}
]
requires-python = ">=3.8"
dependencies = [
    "pandas>=1.5.0",
    "polars>=0.19.0",
    "pyarrow>=10.0.0",
    "numpy>=1.20.0",
]
classifiers = [
    "Programming Language :: Rust",
    "Programming Language :: Python :: Implementation :: CPython",
    "Programming Language :: Python :: Implementation :: PyPy",
]

[tool.maturin]
features = ["pyo3/extension-module"]
